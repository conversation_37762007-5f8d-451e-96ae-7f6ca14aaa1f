import { NextRequest, NextResponse } from "next/server";
import { validateApiKey } from "~/utils/apiAuth/apiKeyUtil";
import { getOusBelowOu } from "~/server/model/ou/func";
import { 
  loadSessionData, 
  loadChargepointstatus, 
  loadLocation,
  getCurrentKw 
} from "~/pages/api/realtime2";

/**
 * API-Endpunkt für aktuelle Ladeleistung aller Ladepunkte einer OU
 * Berechnet die Ladeleistung basierend auf aktiven Sessions (wie auf der Realtime-Seite)
 * URL: GET /api/v1/ou/charging-power
 */
export async function GET(request: NextRequest) {
  try {
    // API-Key Validierung
    const validation = await validateApiKey(request);

    if (!validation.isValid) {
      return NextResponse.json(
        { 
          error: "API key validation failed", 
          message: validation.error 
        },
        { status: 401 }
      );
    }

    const contact = validation.contact;
    const contactOu = contact?.ou;

    if (!contactOu) {
      return NextResponse.json(
        { 
          error: "No OU found for contact",
          message: "Contact ist keiner OU zugeordnet"
        },
        { status: 400 }
      );
    }

    // Alle OUs unterhalb der Contact-OU laden (inklusive der OU selbst)
    const ous = await getOusBelowOu(contactOu);
    const ouIds = ous.map((ou) => ou.id);

    // Session-Daten und Ladepunkt-Status laden
    const activeChargingSessions = await loadSessionData(ouIds);
    const chargepointStatusList = await loadChargepointstatus(ouIds);
    const locations = await loadLocation(ouIds);

    // Gesamte aktuelle Ladeleistung berechnen
    const totalCurrentKw = getCurrentKw(activeChargingSessions);

    // Detaillierte Ladepunkt-Daten erstellen
    const chargingPointDetails = [];
    
    for (const session of activeChargingSessions) {
      // Aktuelle Leistung für diese Session berechnen
      const currentWh = session.chargingMeterValues
        ?.slice()
        .reverse()
        .find((item: any) => item.measurand === "Power.Active.Import");
      
      let currentKw = 0;
      if (currentWh?.value) {
        currentKw = parseFloat(currentWh.value);
        if (currentKw > 0 && currentWh.unit !== "kW") {
          currentKw = currentKw / 1000; // Watt zu kW konvertieren
        }
      }

      // Location-Informationen finden
      const chargePointStatus = chargepointStatusList.find(
        (cp: any) => cp.id === session.chargePointId
      );
      
      const location = locations.find(
        (loc: any) => loc.id === chargePointStatus?.locationId
      );

      chargingPointDetails.push({
        sessionId: session.sessionId,
        chargePointId: session.chargePointId,
        connectorId: session.connectorId,
        evseId: session.evseId,
        currentKw: currentKw,
        totalEnergyInKwh: session.totalEnergyInKwh || 0,
        sessionStartTime: session.sessionStartTime,
        location: {
          id: location?.id,
          name: location?.name,
          address: location?.address,
          city: location?.city,
        },
        ou: {
          id: session.ouId,
          name: ous.find(ou => ou.id === session.ouId)?.name,
          code: ous.find(ou => ou.id === session.ouId)?.code,
        }
      });
    }

    // Gruppierung nach OU
    const powerByOu = ouIds.map(ouId => {
      const ouSessions = activeChargingSessions.filter((session: any) => session.ouId === ouId);
      const ouCurrentKw = getCurrentKw(ouSessions);
      const ou = ous.find(o => o.id === ouId);
      
      return {
        ouId: ouId,
        ouName: ou?.name,
        ouCode: ou?.code,
        currentKw: ouCurrentKw / 1000, // Watt zu kW
        activeSessions: ouSessions.length,
        chargingPoints: chargingPointDetails.filter(cp => cp.ou.id === ouId)
      };
    }).filter(ou => ou.activeSessions > 0); // Nur OUs mit aktiven Sessions

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      contact: {
        id: contact?.id,
        name: contact?.name,
        companyName: contact?.companyName,
        ou: {
          id: contactOu.id,
          name: contactOu.name,
          code: contactOu.code,
        }
      },
      chargingPower: {
        totalCurrentKw: (totalCurrentKw / 1000).toFixed(2), // Watt zu kW, 2 Dezimalstellen
        totalActiveSessions: activeChargingSessions.length,
        totalChargingPoints: chargingPointDetails.length,
        powerByOu: powerByOu,
        chargingPointDetails: chargingPointDetails
      },
      statistics: {
        totalOusChecked: ouIds.length,
        ousWithActiveSessions: powerByOu.length,
        totalActiveSessions: activeChargingSessions.length,
        totalChargingPoints: chargingPointDetails.length,
      }
    });

  } catch (error) {
    console.error("Error fetching charging power:", error);
    return NextResponse.json(
      { 
        error: "Internal server error",
        message: "Fehler beim Abrufen der Ladeleistung"
      },
      { status: 500 }
    );
  }
}
