import React from "react";
import { Role } from "@prisma/client";

export enum MenuType {
  divider,
  menu,
  collapsibleSection,
}

export interface MenuEntry {
  name: string;
  type: MenuType.menu;
  href: string;
  subMenu?: MenuEntry[];
  icon: string;
}

export interface MenuDivider {
  name: string;
  type: MenuType.divider;
  href?: string;
}

export interface CollapsibleMenuSection {
  name: string;
  type: MenuType.collapsibleSection;
  items: MenuItem[];
  defaultCollapsed?: boolean;
  icon?: string;
}

export type MenuItem = MenuEntry | MenuDivider | CollapsibleMenuSection;

export interface RoleMenuDefinition {
  [Role.ADMIN]: MenuItem[];
  [Role.CPO]: MenuItem[];
  [Role.CARD_MANAGER]: MenuItem[];
  [Role.CARD_HOLDER]: MenuItem[];
  [Role.USER]: MenuItem[];
}

// Admin Menu Definition
const adminMenuDef: MenuItem[] = [
  {
    name: "Operations",
    type: MenuType.divider,
  },
  {
    name: "Dashboard",
    type: MenuType.menu,
    href: "/",
    icon: "🎛️",
  },
  {
    name: "Parking",
    type: MenuType.menu,
    href: "/parkingsensor",
    icon: "🅿️",
  },
  {
    name: "Power Graph",
    type: MenuType.menu,
    href: "/power-visualization",
    icon: "📊",
  },
  {
    name: "Financial",
    type: MenuType.divider,
  },
  {
    name: "CPO Revenue Dashboard",
    href: "/cpo-revenue-dashboard",
    type: MenuType.menu,
    icon: "📊",
  },
  {
    name: "CPO Verträge",
    href: "/cpoContract",
    type: MenuType.menu,
    icon: "📄",
  },
  {
    name: "Finance Dashboard",
    href: "/finance-dashboard",
    type: MenuType.menu,
    icon: "💰",
  },
  {
    name: "Forecast",
    href: "/financal-forecast",
    type: MenuType.menu,
    icon: "📈",
  },
  {
    name: "Invoice & Credit",
    href: "/invoice",
    type: MenuType.menu,
    icon: "🧾",
  },
  {
    name: "Invoice forecast",
    href: "/invoice-forecast",
    type: MenuType.menu,
    icon: "🕐",
  },
  {
    name: "Qonto Transactions",
    href: "/qonto-transactions",
    type: MenuType.menu,
    icon: "🏦",
  },
  {
    name: "Stripe",
    href: "/stripe",
    type: MenuType.menu,
    icon: "💳",
  },
  {
    name: "Data",
    type: MenuType.divider,
  },
  {
    name: "API Key Verwaltung",
    href: "/contact/apikey",
    type: MenuType.menu,
    icon: "🔑",
  },
  {
    name: "CDRs",
    href: "/cdr",
    type: MenuType.menu,
    icon: "🗄️",
  },
  {
    name: "EMP & CPO",
    href: "/contact",
    type: MenuType.menu,
    icon: "📇",
  },
  {
    name: "Flottenkarten (Token)",
    href: "/tokenGroup",
    type: MenuType.menu,
    icon: "🔑",
  },
  {
    name: "Locations",
    type: MenuType.menu,
    href: "/location",
    icon: "🏢",
  },
  {
    name: "Mein API Key (Ou)",
    href: "/my-api-key",
    type: MenuType.menu,
    icon: "🔐",
  },
  {
    name: "Tarife",
    href: "/tarif",
    type: MenuType.menu,
    icon: "💰",
  },
  {
    name: "Admin",
    type: MenuType.divider,
  },
  {
    name: "Benutzerverwaltung",
    type: MenuType.menu,
    href: "/users",
    icon: "👥",
  },
  {
    name: "Command",
    type: MenuType.menu,
    icon: "💻",
    href: "/command",
  },
  {
    name: "Log",
    type: MenuType.menu,
    href: "/log",
    icon: "📋",
  },
  {
    name: "OU Verwaltung",
    type: MenuType.menu,
    href: "/ous",
    icon: "🏢",
  },
  {
    name: "Tenant",
    type: MenuType.menu,
    href: "/tenantconfiguration",
    icon: "🏛️",
  },
  {
    name: "Ladekarten-Management",
    type: MenuType.divider,
  },
  {
    name: "Firmentarife Ou",
    type: MenuType.menu,
    href: "/emp/tarif/managerview",
    icon: "🏷️",
  },
  {
    name: "Ladevorgänge Ou",
    type: MenuType.menu,
    href: "/emp/charging-history/managerview",
    icon: "⚡️",
  },
  {
    name: "Mitarbeiter-Ladekarten",
    type: MenuType.menu,
    href: "/emp/card",
    icon: "💳",
  },
  {
    name: "Sonstiges",
    type: MenuType.divider,
  },
  {
    name: "Benachrichtigungen",
    type: MenuType.menu,
    href: "/notifications",
    icon: "🔔",
  },
  {
    name: "Profil",
    type: MenuType.menu,
    href: "/profile",
    icon: "👤",
  },
  {
    name: "Support",
    type: MenuType.menu,
    href: "/support",
    icon: "❓",
  },

  // Einklappbare Sektionen für andere Rollen-Ansichten
  {
    name: "CPO Ansicht",
    type: MenuType.collapsibleSection,
    defaultCollapsed: true,
    icon: "🔌",
    items: [
      {
        name: "Ladevorgänge",
        type: MenuType.menu,
        href: "/emp/charging-history/managerview",
        icon: "📋",
      },
      {
        name: "Maintenance",
        type: MenuType.menu,
        href: "/maintenance",
        icon: "🔧",
      },
      {
        name: "Mein API Key",
        href: "/my-api-key",
        type: MenuType.menu,
        icon: "🔐",
      },
      {
        name: "Roaming Matrix",
        href: "/roaming-matrix",
        type: MenuType.menu,
        icon: "🌐",
      },
      {
        name: "Stromtarife",
        href: "/stromtarife",
        type: MenuType.menu,
        icon: "⚡",
      },
    ],
  },
  {
    name: "Card Manager Ansicht",
    type: MenuType.collapsibleSection,
    defaultCollapsed: true,
    icon: "💳",
    items: [
      {
        name: "Benutzerverwaltung",
        type: MenuType.menu,
        href: "/users",
        icon: "👥",
      },
      {
        name: "Firmentarife",
        type: MenuType.menu,
        href: "/emp/tarif/managerview",
        icon: "🏷️",
      },
      {
        name: "Ladekarten",
        type: MenuType.menu,
        href: "/emp/card",
        icon: "💳",
      },
      {
        name: "Ladevorgänge",
        type: MenuType.menu,
        href: "/emp/charging-history/managerview",
        icon: "📋",
      },
      {
        name: "Mein API Key",
        href: "/my-api-key",
        type: MenuType.menu,
        icon: "🔐",
      },
      {
        name: "Nutzergruppen",
        type: MenuType.menu,
        href: "/userGroups",
        icon: "👥",
      },
      {
        name: "Rechnungen",
        type: MenuType.menu,
        href: "/emp/invoice/managerview",
        icon: "🧾",
      },
      {
        name: "Roaming Matrix",
        href: "/roaming-matrix",
        type: MenuType.menu,
        icon: "🌐",
      },
      {
        name: "Stromtarife",
        href: "/stromtarife",
        type: MenuType.menu,
        icon: "⚡",
      },
    ],
  },
  {
    name: "Card Holder Ansicht",
    type: MenuType.collapsibleSection,
    defaultCollapsed: true,
    icon: "👤",
    items: [
      {
        name: "Home",
        type: MenuType.menu,
        href: "/",
        icon: "🏠",
      },
      {
        name: "Ladekarten",
        type: MenuType.menu,
        href: "/emp/card",
        icon: "💳",
      },
      {
        name: "Ladevorgänge",
        type: MenuType.menu,
        href: "/emp/charging-history/userview",
        icon: "⚡️",
      },
      {
        name: "Mein Tarif",
        type: MenuType.menu,
        href: "/emp/tarif/userview",
        icon: "🏷️",
      },
      {
        name: "Rechnungen",
        type: MenuType.menu,
        href: "/emp/invoice/userview",
        icon: "🧾",
      },
      {
        name: "Zahlungsdaten",
        type: MenuType.menu,
        href: "/emp/payment",
        icon: "💰",
      },
    ],
  },
];

// CPO Menu Definition
const cpoMenuDef: MenuItem[] = [
  {
    name: "Operations",
    type: MenuType.divider,
  },
  {
    name: "Dashboard",
    type: MenuType.menu,
    href: "/",
    icon: "🎛️",
  },
  {
    name: "Power Graph",
    type: MenuType.menu,
    href: "/power-visualization",
    icon: "📊",
  },
  {
    name: "Financial",
    type: MenuType.divider,
  },
  {
    name: "Forecast",
    href: "/financal-forecast",
    type: MenuType.menu,
    icon: "📈",
  },
  {
    name: "Stromtarife",
    href: "/stromtarife",
    type: MenuType.menu,
    icon: "⚡",
  },
  {
    name: "Data",
    type: MenuType.divider,
  },
  {
    name: "Mein API Key",
    href: "/my-api-key",
    type: MenuType.menu,
    icon: "🔐",
  },
  {
    name: "Roaming Matrix",
    href: "/roaming-matrix",
    type: MenuType.menu,
    icon: "🌐",
  },
  {
    name: "Maintenance",
    type: MenuType.menu,
    href: "/maintenance",
    icon: "🔧",
  },
  {
    name: "Ladevorgänge",
    type: MenuType.menu,
    href: "/emp/charging-history/managerview",
    icon: "📋",
  },
  {
    name: "Sonstiges",
    type: MenuType.divider,
  },
  {
    name: "Benachrichtigungen",
    type: MenuType.menu,
    href: "/notifications",
    icon: "🔔",
  },
  {
    name: "Profil",
    type: MenuType.menu,
    href: "/profile",
    icon: "👤",
  },
  {
    name: "Support",
    type: MenuType.menu,
    href: "/support",
    icon: "❓",
  },
];

// Card Manager Menu Definition
const cardManagerMenuDef: MenuItem[] = [
  {
    name: "Operations",
    type: MenuType.divider,
  },
  {
    name: "Dashboard",
    type: MenuType.menu,
    href: "/",
    icon: "🎛️",
  },
  {
    name: "Stromtarife",
    href: "/stromtarife",
    type: MenuType.menu,
    icon: "⚡",
  },
  {
    name: "Data",
    type: MenuType.divider,
  },
  {
    name: "Mein API Key",
    href: "/my-api-key",
    type: MenuType.menu,
    icon: "🔐",
  },
  {
    name: "Roaming Matrix",
    href: "/roaming-matrix",
    type: MenuType.menu,
    icon: "🌐",
  },
  {
    name: "Nutzergruppen",
    type: MenuType.menu,
    href: "/userGroups",
    icon: "👥",
  },
  {
    name: "Ladekarten-Management",
    type: MenuType.divider,
  },
  {
    name: "Ladekarten",
    type: MenuType.menu,
    href: "/emp/card",
    icon: "💳",
  },
  {
    name: "Firmentarife",
    type: MenuType.menu,
    href: "/emp/tarif/managerview",
    icon: "🏷️",
  },
  {
    name: "Ladevorgänge",
    type: MenuType.menu,
    href: "/emp/charging-history/managerview",
    icon: "📋",
  },
  {
    name: "Rechnungen",
    type: MenuType.menu,
    href: "/emp/invoice/managerview",
    icon: "🧾",
  },
  {
    name: "Benutzerverwaltung",
    type: MenuType.menu,
    href: "/users",
    icon: "👥",
  },
  {
    name: "Sonstiges",
    type: MenuType.divider,
  },
  {
    name: "Benachrichtigungen",
    type: MenuType.menu,
    href: "/notifications",
    icon: "🔔",
  },
  {
    name: "Profil",
    type: MenuType.menu,
    href: "/profile",
    icon: "👤",
  },
  {
    name: "Support",
    type: MenuType.menu,
    href: "/support",
    icon: "❓",
  },
];

// Card Holder Menu Definition
const cardHolderMenuDef: MenuItem[] = [
  {
    name: "Home",
    type: MenuType.menu,
    href: "/",
    icon: "🏠",
  },
  {
    name: "Ladekarten-Management",
    type: MenuType.divider,
  },
  {
    name: "Zahlungsdaten",
    type: MenuType.menu,
    href: "/emp/payment",
    icon: "💰",
  },
  {
    name: "Ladekarten",
    type: MenuType.menu,
    href: "/emp/card",
    icon: "💳",
  },
  {
    name: "Mein Tarif",
    type: MenuType.menu,
    href: "/emp/tarif/userview",
    icon: "🏷️",
  },
  {
    name: "Ladevorgänge",
    type: MenuType.menu,
    href: "/emp/charging-history/userview",
    icon: "⚡️",
  },
  {
    name: "Rechnungen",
    type: MenuType.menu,
    href: "/emp/invoice/userview",
    icon: "🧾",
  },
  {
    name: "Sonstiges",
    type: MenuType.divider,
  },
  {
    name: "Benachrichtigungen",
    type: MenuType.menu,
    href: "/notifications",
    icon: "🔔",
  },
  {
    name: "Profil",
    type: MenuType.menu,
    href: "/profile",
    icon: "👤",
  },
  {
    name: "Support",
    type: MenuType.menu,
    href: "/support",
    icon: "❓",
  },
];

// User Menu Definition
const userMenuDef: MenuItem[] = [
  {
    name: "Operations",
    type: MenuType.divider,
  },
  {
    name: "Dashboard",
    type: MenuType.menu,
    href: "/",
    icon: "🎛️",
  },
  {
    name: "Ladekarten-Management",
    type: MenuType.divider,
  },
  {
    name: "Mitarbeiter-Ladekarten",
    type: MenuType.menu,
    href: "/emp/card",
    icon: "💳",
  },
  {
    name: "Sonstiges",
    type: MenuType.divider,
  },
  {
    name: "Benachrichtigungen",
    type: MenuType.menu,
    href: "/notifications",
    icon: "🔔",
  },
  {
    name: "Support",
    type: MenuType.menu,
    href: "/support",
    icon: "❓",
  },
];

// Role-based menu definitions
export const roleMenuDefinitions: RoleMenuDefinition = {
  [Role.ADMIN]: adminMenuDef,
  [Role.CPO]: cpoMenuDef,
  [Role.CARD_MANAGER]: cardManagerMenuDef,
  [Role.CARD_HOLDER]: cardHolderMenuDef,
  [Role.USER]: userMenuDef,
};

// Helper function to get menu for a specific role
export const getMenuForRole = (role: Role): MenuItem[] => {
  return roleMenuDefinitions[role] || [];
};
